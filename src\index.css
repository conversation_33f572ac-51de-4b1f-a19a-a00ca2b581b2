@import url("https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600&display=swap");
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    min-height: 100vh;
    font-family: "Inter", sans-serif;
    background: linear-gradient(to bottom right, #fdf2f8, #ffffff, #fffbeb);
    color: #111827;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: "Playfair Display", serif;
  }
}

@layer components {
  .wedding-gradient {
    background: linear-gradient(to right, #f472b6, #ec4899, #fbbf24);
  }

  .wedding-text-gradient {
    background: linear-gradient(to right, #db2777, #be185d, #d97706);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
  }

  .glass-effect {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  }

  .btn-primary {
    padding: 0.75rem 1.5rem;
    background: linear-gradient(to right, #ec4899, #f59e0b);
    color: white;
    font-weight: 500;
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s;
    outline: none;
  }

  .btn-primary:hover {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    transform: scale(1.05);
  }

  .btn-primary:focus {
    box-shadow: 0 0 0 2px #f472b6, 0 0 0 4px rgba(244, 114, 182, 0.1);
  }

  .btn-secondary {
    padding: 0.75rem 1.5rem;
    background: rgba(255, 255, 255, 0.9);
    color: #be185d;
    font-weight: 500;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #fbcfe8;
    transition: all 0.2s;
    outline: none;
  }

  .btn-secondary:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    border-color: #f9a8d4;
    transform: scale(1.05);
  }

  .btn-secondary:focus {
    box-shadow: 0 0 0 2px #f472b6, 0 0 0 4px rgba(244, 114, 182, 0.1);
  }
}
