import React, { useCallback, useState, useEffect } from "react";
import { useDropzone } from "react-dropzone";
import {
  compressImages,
  CompressionResult,
  formatFileSize,
} from "../utils/imageCompression";
import { useGoogleDrive } from "../hooks/useGoogleDrive";
import { GoogleDriveSetup } from "./GoogleDriveSetup";
import { Gallery } from "./Gallery";

interface FileUploadProps {
  onFilesUploaded?: (files: File[]) => void;
  processedFiles?: ProcessedFile[];
  onProcessedFilesChange?: (files: ProcessedFile[]) => void;
}

interface ProcessedFile {
  original: File;
  compressed?: File;
  compressionResult?: CompressionResult;
}

// Common smartphone photo and video formats
const ACCEPTED_FILE_TYPES = {
  // Image formats
  "image/jpeg": [".jpg", ".jpeg"],
  "image/png": [".png"],
  "image/heic": [".heic", ".heif"], // iPhone photos
  "image/webp": [".webp"],
  "image/avif": [".avif"],

  // Video formats
  "video/mp4": [".mp4"],
  "video/quicktime": [".mov"], // iPhone videos
  "video/webm": [".webm"],
  "video/avi": [".avi"],
  "video/3gpp": [".3gp"], // Some Android phones
  "video/x-msvideo": [".avi"],
};

const MAX_FILE_SIZE = 100 * 1024 * 1024; // 100MB per file

export const FileUpload: React.FC<FileUploadProps> = ({
  onFilesUploaded,
  processedFiles: externalProcessedFiles,
  onProcessedFilesChange,
}) => {
  const [internalProcessedFiles, setInternalProcessedFiles] = useState<
    ProcessedFile[]
  >([]);

  // Use external processed files if provided, otherwise use internal state
  const processedFiles = externalProcessedFiles || internalProcessedFiles;
  const setProcessedFiles = onProcessedFilesChange || setInternalProcessedFiles;
  const [isProcessing, setIsProcessing] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);
  const [compressionProgress, setCompressionProgress] = useState<{
    progress: number;
    currentFile: string;
  } | null>(null);
  const [showGoogleDriveSetup, setShowGoogleDriveSetup] = useState(false);

  // Google Drive integration
  const googleDrive = useGoogleDrive();

  // Initialize Google Drive on component mount
  useEffect(() => {
    googleDrive.initialize();
  }, []);

  const onDrop = useCallback(
    (acceptedFiles: File[], rejectedFiles: any[]) => {
      setUploadError(null);

      if (rejectedFiles.length > 0) {
        const errors = rejectedFiles.map((file) => {
          if (
            file.errors.some((error: any) => error.code === "file-too-large")
          ) {
            return `${file.file.name} is too large (max 100MB)`;
          }
          if (
            file.errors.some((error: any) => error.code === "file-invalid-type")
          ) {
            return `${file.file.name} is not a supported format`;
          }
          return `${file.file.name} could not be uploaded`;
        });
        setUploadError(errors.join(", "));
      }

      if (acceptedFiles.length > 0) {
        setIsProcessing(true);

        // Process and compress files
        (async () => {
          try {
            const compressionResults = await compressImages(
              acceptedFiles,
              {},
              (progress, currentFile) => {
                setCompressionProgress({ progress, currentFile });
              },
            );

            const newProcessedFiles: ProcessedFile[] = compressionResults.map(
              (result) => ({
                original: result.originalFile,
                compressed: result.compressedFile,
                compressionResult: result,
              }),
            );

            setProcessedFiles((prev) => [...prev, ...newProcessedFiles]);
            setCompressionProgress(null);

            const filesToUpload = compressionResults.map(
              (result) => result.compressedFile,
            );

            // Upload to Google Drive if available and user is signed in
            if (
              googleDrive.state.isInitialized &&
              googleDrive.state.isSignedIn
            ) {
              try {
                const uploadResults = await googleDrive.uploadFiles(
                  filesToUpload,
                );
                const successfulUploads = uploadResults.filter(
                  (result) => result.success,
                );

                if (successfulUploads.length > 0) {
                  console.log(
                    `Successfully uploaded ${successfulUploads.length} files to Google Drive`,
                  );
                }

                const failedUploads = uploadResults.filter(
                  (result) => !result.success,
                );
                if (failedUploads.length > 0) {
                  console.warn(
                    `Failed to upload ${failedUploads.length} files to Google Drive`,
                  );
                }
              } catch (error) {
                console.error("Google Drive upload failed:", error);
              }
            }

            onFilesUploaded?.(filesToUpload);
          } catch (error) {
            console.error("File processing failed:", error);
            setUploadError("Failed to process some files. Please try again.");
          } finally {
            setIsProcessing(false);
          }
        })();
      }
    },
    [onFilesUploaded],
  );

  const { getRootProps, getInputProps, isDragActive, isDragReject } =
    useDropzone({
      onDrop,
      accept: ACCEPTED_FILE_TYPES,
      maxSize: MAX_FILE_SIZE,
      multiple: true,
    });

  const removeFile = (index: number) => {
    setProcessedFiles((prev) => prev.filter((_, i) => i !== index));
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  return (
    <div className="w-full max-w-4xl mx-auto">
      {/* Dropzone */}
      <div
        {...getRootProps()}
        className={`
          relative border-2 border-dashed rounded-3xl p-8 md:p-12 text-center cursor-pointer
          transition-all duration-300 ease-in-out
          ${
            isDragActive && !isDragReject
              ? "border-rose-400 bg-rose-50/50 scale-105"
              : isDragReject
              ? "border-red-400 bg-red-50/50"
              : "border-rose-200 hover:border-rose-300 hover:bg-rose-50/30"
          }
          ${isProcessing ? "pointer-events-none opacity-75" : ""}
        `}
      >
        <input {...getInputProps()} />

        {/* Upload icon */}
        <div className="mb-6">
          <div className="w-16 h-16 mx-auto bg-gradient-to-br from-rose-400 to-gold-400 rounded-full flex items-center justify-center mb-4">
            <svg
              className="w-8 h-8 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
              />
            </svg>
          </div>
        </div>

        {/* Upload text */}
        <div className="space-y-4">
          {isProcessing ? (
            <div className="space-y-2">
              <div className="animate-spin w-8 h-8 border-4 border-rose-200 border-t-rose-500 rounded-full mx-auto"></div>
              {compressionProgress ? (
                <div className="space-y-2">
                  <p className="text-lg font-medium text-rose-600">
                    Processing {compressionProgress.currentFile}...
                  </p>
                  <div className="w-full bg-rose-100 rounded-full h-2">
                    <div
                      className="bg-rose-500 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${compressionProgress.progress}%` }}
                    ></div>
                  </div>
                  <p className="text-sm text-rose-500">
                    {compressionProgress.progress}% complete
                  </p>
                </div>
              ) : (
                <p className="text-lg font-medium text-rose-600">
                  Processing your memories...
                </p>
              )}
            </div>
          ) : isDragActive ? (
            isDragReject ? (
              <p className="text-lg font-medium text-red-600">
                Some files are not supported. Please drop only photos and
                videos.
              </p>
            ) : (
              <p className="text-lg font-medium text-rose-600">
                Drop your beautiful memories here! 📸
              </p>
            )
          ) : (
            <>
              <h3 className="text-2xl md:text-3xl font-serif text-rose-800 mb-4">
                Share Your Wedding Memories
              </h3>
              <p className="text-lg text-gray-700 mb-4">
                Drag & drop your photos and videos here, or click to browse
              </p>
              <p className="text-sm text-gray-500">
                Supports: JPG, PNG, HEIC, MP4, MOV, and more • Max 100MB per
                file
              </p>
            </>
          )}
        </div>

        {/* Browse button */}
        {!isProcessing && (
          <button className="btn-secondary mt-6">Browse Files</button>
        )}
      </div>

      {/* Google Drive Status */}
      <div className="mt-6 p-4 glass-effect rounded-xl">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-serif text-rose-800">
            Google Drive Integration
          </h3>
          <button
            onClick={() => setShowGoogleDriveSetup(true)}
            className="text-sm text-rose-600 hover:text-rose-700 underline"
          >
            Setup Guide
          </button>
        </div>

        {googleDrive.state.error ? (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <p className="text-yellow-800 text-sm mb-2">
              {googleDrive.state.error}
            </p>
            <button
              onClick={() => setShowGoogleDriveSetup(true)}
              className="text-sm text-yellow-700 hover:text-yellow-800 underline"
            >
              View Setup Instructions
            </button>
          </div>
        ) : googleDrive.state.isInitialized ? (
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="text-sm text-green-700 font-medium">
                Google Drive API Ready
              </span>
            </div>

            {googleDrive.state.isSignedIn ? (
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm text-green-700">
                    Signed in to Google Drive
                  </span>
                </div>
                <button
                  onClick={googleDrive.signOut}
                  className="text-sm text-gray-600 hover:text-gray-700 underline"
                >
                  Sign Out
                </button>
              </div>
            ) : (
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <span className="text-sm text-yellow-700">Not signed in</span>
                </div>
                <button
                  onClick={googleDrive.signIn}
                  className="btn-secondary text-sm py-2 px-4"
                >
                  Sign In to Google Drive
                </button>
              </div>
            )}

            {googleDrive.state.uploadProgress && (
              <div className="space-y-2">
                <p className="text-sm text-rose-600">
                  Uploading to Google Drive:{" "}
                  {googleDrive.state.uploadProgress.currentFile}
                </p>
                <div className="w-full bg-rose-100 rounded-full h-2">
                  <div
                    className="bg-rose-500 h-2 rounded-full transition-all duration-300"
                    style={{
                      width: `${
                        (googleDrive.state.uploadProgress.completed /
                          googleDrive.state.uploadProgress.total) *
                        100
                      }%`,
                    }}
                  ></div>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-gray-400 rounded-full"></div>
            <span className="text-sm text-gray-600">
              Initializing Google Drive...
            </span>
          </div>
        )}
      </div>

      {/* Error message */}
      {uploadError && (
        <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-700 text-sm">{uploadError}</p>
        </div>
      )}

      {/* Processed files list */}
      {processedFiles.length > 0 && (
        <div className="mt-8">
          <h4 className="text-xl font-serif text-rose-800 mb-4">
            Processed Files ({processedFiles.length})
          </h4>
          <div className="space-y-2">
            {processedFiles.map((processedFile, index) => (
              <div
                key={index}
                className="flex items-center justify-between p-3 bg-white/80 rounded-lg border border-rose-100"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-rose-100 rounded-lg flex items-center justify-center">
                    {processedFile.original.type.startsWith("image/") ? (
                      <svg
                        className="w-5 h-5 text-rose-600"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path
                          fillRule="evenodd"
                          d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z"
                          clipRule="evenodd"
                        />
                      </svg>
                    ) : (
                      <svg
                        className="w-5 h-5 text-rose-600"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path d="M2 6a2 2 0 012-2h6l2 2h6a2 2 0 012 2v6a2 2 0 01-2 2H4a2 2 0 01-2-2V6z" />
                        <path
                          stroke="currentColor"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth="2"
                          d="m7 10 2 2 4-4"
                        />
                      </svg>
                    )}
                  </div>
                  <div>
                    <p className="font-medium text-gray-900 truncate max-w-xs">
                      {processedFile.original.name}
                    </p>
                    <div className="flex items-center space-x-2 text-sm">
                      <span className="text-gray-500">
                        {formatFileSize(processedFile.original.size)}
                      </span>
                      {processedFile.compressionResult &&
                        processedFile.compressionResult.compressionRatio >
                          0 && (
                          <>
                            <span className="text-gray-400">→</span>
                            <span className="text-green-600 font-medium">
                              {formatFileSize(
                                processedFile.compressionResult.compressedSize,
                              )}
                            </span>
                            <span className="text-green-600 text-xs bg-green-100 px-2 py-1 rounded-full">
                              -
                              {processedFile.compressionResult.compressionRatio}
                              %
                            </span>
                          </>
                        )}
                    </div>
                  </div>
                </div>
                <button
                  onClick={() => removeFile(index)}
                  className="text-red-500 hover:text-red-700 p-1"
                  title="Remove file"
                >
                  <svg
                    className="w-5 h-5"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M6 18L18 6M6 6l12 12"
                    />
                  </svg>
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Gallery */}
      {processedFiles.length > 0 && (
        <Gallery
          files={processedFiles.map((pf) => pf.compressed || pf.original)}
        />
      )}

      {/* Google Drive Setup Modal */}
      {showGoogleDriveSetup && (
        <GoogleDriveSetup onClose={() => setShowGoogleDriveSetup(false)} />
      )}
    </div>
  );
};
