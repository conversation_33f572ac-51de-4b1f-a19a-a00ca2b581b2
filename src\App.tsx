import React, { useState } from "react";
import { FileUpload } from "./components/FileUpload";
import { AdminPanel } from "./components/AdminPanel";

interface ProcessedFile {
  original: File;
  compressed?: File;
  compressionResult?: any;
}

function App() {
  const [showUpload, setShowUpload] = useState(false);
  const [showAdmin, setShowAdmin] = useState(false);
  const [uploadsEnabled, setUploadsEnabled] = useState(true);
  const [processedFiles, setProcessedFiles] = useState<ProcessedFile[]>([]);

  const handleFilesUploaded = (files: File[]) => {
    console.log("Files uploaded:", files);
    // Here we'll later integrate with Google Drive
  };

  const handleRemoveFile = (index: number) => {
    setProcessedFiles((prev) => prev.filter((_, i) => i !== index));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-rose-50 via-white to-gold-50">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-20 left-10 w-32 h-32 bg-rose-200/30 rounded-full blur-xl animate-pulse-slow"></div>
        <div className="absolute top-40 right-20 w-24 h-24 bg-gold-200/30 rounded-full blur-xl animate-pulse-slow delay-1000"></div>
        <div className="absolute bottom-32 left-1/4 w-40 h-40 bg-rose-100/40 rounded-full blur-2xl animate-pulse-slow delay-2000"></div>
        <div className="absolute bottom-20 right-1/3 w-28 h-28 bg-gold-100/40 rounded-full blur-xl animate-pulse-slow delay-3000"></div>
      </div>

      {/* Main content */}
      <div className="relative z-10 flex flex-col items-center justify-center min-h-screen px-4 py-8">
        {/* Header section */}
        <div className="text-center mb-12 animate-fade-in">
          {/* Decorative element */}
          <div className="flex justify-center mb-6">
            <div className="w-16 h-0.5 bg-gradient-to-r from-transparent via-rose-400 to-transparent"></div>
            <div className="mx-4 text-rose-400 text-2xl">❤</div>
            <div className="w-16 h-0.5 bg-gradient-to-r from-transparent via-rose-400 to-transparent"></div>
          </div>

          {/* Names */}
          <h1 className="text-5xl md:text-7xl lg:text-8xl font-serif font-bold wedding-text-gradient mb-4 tracking-wide">
            Karolina
          </h1>
          <div className="text-3xl md:text-4xl text-rose-400 font-light mb-4">
            &
          </div>
          <h1 className="text-5xl md:text-7xl lg:text-8xl font-serif font-bold wedding-text-gradient mb-8 tracking-wide">
            Marcin
          </h1>

          {/* Date */}
          <div className="glass-effect rounded-2xl px-8 py-4 inline-block mb-8">
            <p className="text-2xl md:text-3xl font-serif text-rose-700 font-medium">
              11.10.2025
            </p>
          </div>
        </div>

        {/* Wedding message */}
        <div className="text-center max-w-2xl mx-auto mb-12 animate-slide-up">
          <div className="glass-effect rounded-3xl p-8 md:p-12">
            <h2 className="text-2xl md:text-3xl font-serif text-rose-800 mb-6 leading-relaxed">
              Celebrate Our Special Day
            </h2>
            <p className="text-lg md:text-xl text-gray-700 leading-relaxed mb-6">
              We invite you to share in the joy of our wedding celebration. Your
              presence makes our day complete, and your memories make it
              eternal.
            </p>
            <p className="text-base md:text-lg text-rose-600 font-medium italic">
              "Love is not just looking at each other, it's looking in the same
              direction together."
            </p>
          </div>
        </div>

        {/* Call to action section */}
        <div className="text-center animate-slide-up delay-300">
          <div className="glass-effect rounded-2xl p-6 md:p-8 max-w-md mx-auto">
            <h3 className="text-xl md:text-2xl font-serif text-rose-800 mb-4">
              Share Your Memories
            </h3>
            <p className="text-gray-600 mb-6 leading-relaxed">
              Help us capture every beautiful moment by sharing your photos and
              videos from our special day.
            </p>
            <button
              className="btn-primary w-full md:w-auto"
              onClick={() => setShowUpload(true)}
            >
              Upload Photos & Videos
            </button>
          </div>
        </div>

        {/* Footer decorative element */}
        <div className="mt-16 flex justify-center animate-fade-in delay-500">
          <div className="flex items-center space-x-4 text-rose-300">
            <div className="w-12 h-0.5 bg-gradient-to-r from-transparent to-rose-300"></div>
            <span className="text-sm font-light tracking-widest">
              FOREVER TOGETHER
            </span>
            <div className="w-12 h-0.5 bg-gradient-to-l from-transparent to-rose-300"></div>
          </div>
        </div>

        {/* Admin Access */}
        <div className="mt-8 flex justify-center">
          <button
            onClick={() => setShowAdmin(true)}
            className="text-xs text-gray-400 hover:text-gray-600 transition-colors"
          >
            Admin
          </button>
        </div>
      </div>

      {/* File Upload Section */}
      {showUpload && (
        <div className="relative z-10 px-4 py-16 bg-white/50 backdrop-blur-sm">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-serif wedding-text-gradient mb-4">
                Upload Your Memories
              </h2>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Share the beautiful moments you captured during our special day.
                Every photo and video helps us relive the magic of our wedding.
              </p>
            </div>

            {uploadsEnabled ? (
              <FileUpload
                onFilesUploaded={handleFilesUploaded}
                processedFiles={processedFiles}
                onProcessedFilesChange={setProcessedFiles}
              />
            ) : (
              <div className="text-center py-12">
                <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg
                    className="w-8 h-8 text-gray-400"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L5.636 5.636"
                    />
                  </svg>
                </div>
                <h3 className="text-xl font-serif text-gray-600 mb-2">
                  Uploads Temporarily Disabled
                </h3>
                <p className="text-gray-500">
                  File uploads have been temporarily disabled by the wedding
                  couple. Please check back later or contact them directly.
                </p>
              </div>
            )}

            <div className="text-center mt-8">
              <button
                className="btn-secondary"
                onClick={() => setShowUpload(false)}
              >
                Back to Main Page
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Admin Panel */}
      <AdminPanel
        isOpen={showAdmin}
        onClose={() => setShowAdmin(false)}
        processedFiles={processedFiles}
        onRemoveFile={handleRemoveFile}
        uploadsEnabled={uploadsEnabled}
        onToggleUploads={setUploadsEnabled}
      />
    </div>
  );
}

export default App;
