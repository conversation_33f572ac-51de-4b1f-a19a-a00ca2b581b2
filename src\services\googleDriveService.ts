// Google Drive API Service
// This service handles uploading files to Google Drive

export interface GoogleDriveConfig {
  apiKey: string;
  clientId: string;
  folderId: string; // The specific folder ID where wedding photos will be uploaded
}

export interface UploadResult {
  success: boolean;
  fileId?: string;
  fileName: string;
  error?: string;
}

export class GoogleDriveService {
  private config: GoogleDriveConfig;
  private isInitialized = false;

  constructor(config: GoogleDriveConfig) {
    this.config = config;
  }

  /**
   * Initialize the Google Drive API
   */
  async initialize(): Promise<boolean> {
    try {
      // Load Google API script if not already loaded
      if (!window.gapi) {
        await this.loadGoogleAPI();
      }

      // Initialize the API
      await new Promise<void>((resolve, reject) => {
        window.gapi.load('client:auth2', async () => {
          try {
            await window.gapi.client.init({
              apiKey: this.config.apiKey,
              clientId: this.config.clientId,
              discoveryDocs: ['https://www.googleapis.com/discovery/v1/apis/drive/v3/rest'],
              scope: 'https://www.googleapis.com/auth/drive.file'
            });
            resolve();
          } catch (error) {
            reject(error);
          }
        });
      });

      this.isInitialized = true;
      return true;
    } catch (error) {
      console.error('Failed to initialize Google Drive API:', error);
      return false;
    }
  }

  /**
   * Load Google API script dynamically
   */
  private loadGoogleAPI(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (document.querySelector('script[src*="apis.google.com"]')) {
        resolve();
        return;
      }

      const script = document.createElement('script');
      script.src = 'https://apis.google.com/js/api.js';
      script.onload = () => resolve();
      script.onerror = () => reject(new Error('Failed to load Google API'));
      document.head.appendChild(script);
    });
  }

  /**
   * Check if user is signed in
   */
  isSignedIn(): boolean {
    if (!this.isInitialized || !window.gapi?.auth2) {
      return false;
    }
    return window.gapi.auth2.getAuthInstance().isSignedIn.get();
  }

  /**
   * Sign in to Google
   */
  async signIn(): Promise<boolean> {
    if (!this.isInitialized) {
      throw new Error('Google Drive API not initialized');
    }

    try {
      const authInstance = window.gapi.auth2.getAuthInstance();
      await authInstance.signIn();
      return true;
    } catch (error) {
      console.error('Sign in failed:', error);
      return false;
    }
  }

  /**
   * Sign out from Google
   */
  async signOut(): Promise<void> {
    if (!this.isInitialized) return;
    
    const authInstance = window.gapi.auth2.getAuthInstance();
    await authInstance.signOut();
  }

  /**
   * Upload a file to Google Drive
   */
  async uploadFile(file: File): Promise<UploadResult> {
    if (!this.isInitialized) {
      return {
        success: false,
        fileName: file.name,
        error: 'Google Drive API not initialized'
      };
    }

    if (!this.isSignedIn()) {
      return {
        success: false,
        fileName: file.name,
        error: 'User not signed in'
      };
    }

    try {
      // Create file metadata
      const metadata = {
        name: `wedding_${Date.now()}_${file.name}`,
        parents: [this.config.folderId], // Upload to specific folder
      };

      // Convert file to base64
      const base64Data = await this.fileToBase64(file);
      
      // Upload file
      const response = await window.gapi.client.request({
        path: 'https://www.googleapis.com/upload/drive/v3/files',
        method: 'POST',
        params: {
          uploadType: 'multipart'
        },
        headers: {
          'Content-Type': 'multipart/related; boundary="foo_bar_baz"'
        },
        body: this.createMultipartBody(metadata, base64Data, file.type)
      });

      return {
        success: true,
        fileId: response.result.id,
        fileName: file.name
      };
    } catch (error) {
      console.error('Upload failed:', error);
      return {
        success: false,
        fileName: file.name,
        error: error instanceof Error ? error.message : 'Upload failed'
      };
    }
  }

  /**
   * Upload multiple files
   */
  async uploadFiles(
    files: File[], 
    onProgress?: (completed: number, total: number, currentFile: string) => void
  ): Promise<UploadResult[]> {
    const results: UploadResult[] = [];
    
    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      onProgress?.(i, files.length, file.name);
      
      const result = await this.uploadFile(file);
      results.push(result);
      
      // Small delay between uploads to avoid rate limiting
      if (i < files.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }
    
    onProgress?.(files.length, files.length, 'Complete');
    return results;
  }

  /**
   * Convert file to base64
   */
  private fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        // Remove data URL prefix
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  /**
   * Create multipart body for file upload
   */
  private createMultipartBody(metadata: any, data: string, contentType: string): string {
    const delimiter = 'foo_bar_baz';
    const close_delim = `\r\n--${delimiter}--`;
    
    let body = `--${delimiter}\r\n`;
    body += 'Content-Type: application/json\r\n\r\n';
    body += JSON.stringify(metadata) + '\r\n';
    body += `--${delimiter}\r\n`;
    body += `Content-Type: ${contentType}\r\n`;
    body += 'Content-Transfer-Encoding: base64\r\n\r\n';
    body += data;
    body += close_delim;
    
    return body;
  }
}

// Global type declarations for Google API
declare global {
  interface Window {
    gapi: any;
  }
}

// Default configuration (to be replaced with actual values)
export const defaultGoogleDriveConfig: GoogleDriveConfig = {
  apiKey: process.env.REACT_APP_GOOGLE_API_KEY || '',
  clientId: process.env.REACT_APP_GOOGLE_CLIENT_ID || '',
  folderId: process.env.REACT_APP_GOOGLE_DRIVE_FOLDER_ID || '',
};
