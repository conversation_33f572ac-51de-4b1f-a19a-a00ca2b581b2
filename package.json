{"name": "wedding-photo-page", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"browser-image-compression": "^2.0.2", "googleapis": "^161.0.0", "react": "^19.1.1", "react-dom": "^19.1.1", "react-dropzone": "^14.3.8"}, "devDependencies": {"@eslint/js": "^9.36.0", "@tailwindcss/postcss": "^4.1.14", "@types/node": "^24.6.2", "@types/react": "^19.1.16", "@types/react-dom": "^19.1.9", "@types/react-dropzone": "^4.2.2", "@vitejs/plugin-react": "^5.0.4", "autoprefixer": "^10.4.21", "eslint": "^9.36.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.22", "globals": "^16.4.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.14", "typescript": "~5.9.3", "typescript-eslint": "^8.45.0", "vite": "^7.1.7"}}