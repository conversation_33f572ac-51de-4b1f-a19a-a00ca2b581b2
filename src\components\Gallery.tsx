import React, { useState, useEffect, useCallback } from 'react';

interface GalleryItem {
  id: string;
  file: File;
  url: string;
  type: 'image' | 'video';
  thumbnail?: string;
}

interface GalleryProps {
  files: File[];
}

export const Gallery: React.FC<GalleryProps> = ({ files }) => {
  const [galleryItems, setGalleryItems] = useState<GalleryItem[]>([]);
  const [selectedIndex, setSelectedIndex] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Convert files to gallery items with thumbnails
  useEffect(() => {
    const processFiles = async () => {
      setIsLoading(true);
      const items: GalleryItem[] = [];

      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        const url = URL.createObjectURL(file);
        const type = file.type.startsWith('image/') ? 'image' : 'video';
        
        let thumbnail = url;
        
        // For videos, we could generate a thumbnail here
        // For now, we'll use a placeholder or the video itself
        if (type === 'video') {
          thumbnail = await generateVideoThumbnail(file);
        }

        items.push({
          id: `${i}-${file.name}`,
          file,
          url,
          type,
          thumbnail,
        });
      }

      setGalleryItems(items);
      setIsLoading(false);
    };

    if (files.length > 0) {
      processFiles();
    } else {
      setGalleryItems([]);
      setIsLoading(false);
    }

    // Cleanup URLs when component unmounts or files change
    return () => {
      galleryItems.forEach(item => {
        URL.revokeObjectURL(item.url);
        if (item.thumbnail && item.thumbnail !== item.url) {
          URL.revokeObjectURL(item.thumbnail);
        }
      });
    };
  }, [files]);

  // Generate video thumbnail
  const generateVideoThumbnail = (file: File): Promise<string> => {
    return new Promise((resolve) => {
      const video = document.createElement('video');
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      video.addEventListener('loadedmetadata', () => {
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        
        video.currentTime = 1; // Seek to 1 second
      });

      video.addEventListener('seeked', () => {
        if (ctx) {
          ctx.drawImage(video, 0, 0);
          const thumbnailUrl = canvas.toDataURL('image/jpeg', 0.7);
          resolve(thumbnailUrl);
        } else {
          resolve(URL.createObjectURL(file)); // Fallback to video file
        }
      });

      video.addEventListener('error', () => {
        resolve(URL.createObjectURL(file)); // Fallback to video file
      });

      video.src = URL.createObjectURL(file);
      video.load();
    });
  };

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (selectedIndex === null) return;

      switch (event.key) {
        case 'ArrowLeft':
          event.preventDefault();
          setSelectedIndex(prev => 
            prev === null ? null : Math.max(0, prev - 1)
          );
          break;
        case 'ArrowRight':
          event.preventDefault();
          setSelectedIndex(prev => 
            prev === null ? null : Math.min(galleryItems.length - 1, prev + 1)
          );
          break;
        case 'Escape':
          event.preventDefault();
          setSelectedIndex(null);
          break;
      }
    };

    if (selectedIndex !== null) {
      document.addEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'hidden'; // Prevent background scrolling
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
      document.body.style.overflow = 'unset';
    };
  }, [selectedIndex, galleryItems.length]);

  const openLightbox = useCallback((index: number) => {
    setSelectedIndex(index);
  }, []);

  const closeLightbox = useCallback(() => {
    setSelectedIndex(null);
  }, []);

  const goToPrevious = useCallback(() => {
    setSelectedIndex(prev => 
      prev === null ? null : Math.max(0, prev - 1)
    );
  }, []);

  const goToNext = useCallback(() => {
    setSelectedIndex(prev => 
      prev === null ? null : Math.min(galleryItems.length - 1, prev + 1)
    );
  }, [galleryItems.length]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin w-8 h-8 border-4 border-rose-200 border-t-rose-500 rounded-full"></div>
        <span className="ml-3 text-rose-600">Loading gallery...</span>
      </div>
    );
  }

  if (galleryItems.length === 0) {
    return null;
  }

  return (
    <>
      {/* Gallery Grid */}
      <div className="mt-12">
        <h3 className="text-2xl font-serif text-rose-800 mb-6 text-center">
          Uploaded Memories ({galleryItems.length})
        </h3>
        
        <div className="columns-1 sm:columns-2 md:columns-3 lg:columns-4 gap-4 space-y-4">
          {galleryItems.map((item, index) => (
            <div
              key={item.id}
              className="break-inside-avoid cursor-pointer group relative overflow-hidden rounded-lg shadow-md hover:shadow-xl transition-all duration-300 transform hover:scale-105"
              onClick={() => openLightbox(index)}
            >
              {item.type === 'image' ? (
                <img
                  src={item.thumbnail}
                  alt={`Memory ${index + 1}`}
                  className="w-full h-auto object-cover"
                  loading="lazy"
                />
              ) : (
                <div className="relative">
                  <img
                    src={item.thumbnail}
                    alt={`Video ${index + 1}`}
                    className="w-full h-auto object-cover"
                    loading="lazy"
                  />
                  <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
                    <div className="w-12 h-12 bg-white/90 rounded-full flex items-center justify-center">
                      <svg className="w-6 h-6 text-rose-600 ml-1" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M6.3 2.841A1.5 1.5 0 004 4.11V15.89a1.5 1.5 0 002.3 1.269l9.344-5.89a1.5 1.5 0 000-2.538L6.3 2.84z" />
                      </svg>
                    </div>
                  </div>
                </div>
              )}
              
              {/* Overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div className="absolute bottom-2 left-2 right-2">
                  <p className="text-white text-sm font-medium truncate">
                    {item.file.name}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Lightbox */}
      {selectedIndex !== null && (
        <div className="fixed inset-0 bg-black/95 z-50 flex items-center justify-center">
          {/* Close button */}
          <button
            onClick={closeLightbox}
            className="absolute top-4 right-4 z-10 w-12 h-12 bg-white/10 hover:bg-white/20 rounded-full flex items-center justify-center text-white transition-colors"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>

          {/* Navigation buttons */}
          {selectedIndex > 0 && (
            <button
              onClick={goToPrevious}
              className="absolute left-4 top-1/2 -translate-y-1/2 z-10 w-12 h-12 bg-white/10 hover:bg-white/20 rounded-full flex items-center justify-center text-white transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            </button>
          )}

          {selectedIndex < galleryItems.length - 1 && (
            <button
              onClick={goToNext}
              className="absolute right-4 top-1/2 -translate-y-1/2 z-10 w-12 h-12 bg-white/10 hover:bg-white/20 rounded-full flex items-center justify-center text-white transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </button>
          )}

          {/* Media content */}
          <div className="max-w-[90vw] max-h-[90vh] flex items-center justify-center">
            {galleryItems[selectedIndex].type === 'image' ? (
              <img
                src={galleryItems[selectedIndex].url}
                alt={`Memory ${selectedIndex + 1}`}
                className="max-w-full max-h-full object-contain"
              />
            ) : (
              <video
                src={galleryItems[selectedIndex].url}
                controls
                className="max-w-full max-h-full"
                autoPlay
              />
            )}
          </div>

          {/* Info bar */}
          <div className="absolute bottom-4 left-1/2 -translate-x-1/2 bg-white/10 backdrop-blur-sm rounded-full px-6 py-2">
            <p className="text-white text-sm">
              {selectedIndex + 1} of {galleryItems.length} • {galleryItems[selectedIndex].file.name}
            </p>
          </div>

          {/* Keyboard hints */}
          <div className="absolute top-4 left-4 text-white/70 text-sm">
            <p>← → Navigate • ESC Close</p>
          </div>
        </div>
      )}
    </>
  );
};
