import React, { useState } from 'react';

interface GoogleDriveSetupProps {
  onClose: () => void;
}

export const GoogleDriveSetup: React.FC<GoogleDriveSetupProps> = ({ onClose }) => {
  const [currentStep, setCurrentStep] = useState(1);

  const steps = [
    {
      title: "Create Google Cloud Project",
      content: (
        <div className="space-y-4">
          <p className="text-gray-700">
            First, you need to create a Google Cloud Project and enable the Google Drive API.
          </p>
          <ol className="list-decimal list-inside space-y-2 text-sm text-gray-600">
            <li>Go to the <a href="https://console.cloud.google.com/" target="_blank" rel="noopener noreferrer" className="text-rose-600 hover:text-rose-700 underline">Google Cloud Console</a></li>
            <li>Create a new project or select an existing one</li>
            <li>Enable the Google Drive API for your project</li>
            <li>Go to "APIs & Services" → "Credentials"</li>
            <li>Create credentials (API Key and OAuth 2.0 Client ID)</li>
          </ol>
        </div>
      )
    },
    {
      title: "Configure OAuth Consent Screen",
      content: (
        <div className="space-y-4">
          <p className="text-gray-700">
            Configure the OAuth consent screen for your application.
          </p>
          <ol className="list-decimal list-inside space-y-2 text-sm text-gray-600">
            <li>Go to "APIs & Services" → "OAuth consent screen"</li>
            <li>Choose "External" user type</li>
            <li>Fill in the required information:
              <ul className="list-disc list-inside ml-4 mt-2 space-y-1">
                <li>App name: "Wedding Photo Upload"</li>
                <li>User support email: Your email</li>
                <li>Developer contact information: Your email</li>
              </ul>
            </li>
            <li>Add scopes: <code className="bg-gray-100 px-2 py-1 rounded text-xs">https://www.googleapis.com/auth/drive.file</code></li>
          </ol>
        </div>
      )
    },
    {
      title: "Create Google Drive Folder",
      content: (
        <div className="space-y-4">
          <p className="text-gray-700">
            Create a dedicated folder in Google Drive for wedding photos.
          </p>
          <ol className="list-decimal list-inside space-y-2 text-sm text-gray-600">
            <li>Go to <a href="https://drive.google.com" target="_blank" rel="noopener noreferrer" className="text-rose-600 hover:text-rose-700 underline">Google Drive</a></li>
            <li>Create a new folder named "Wedding Photos - Karolina & Marcin"</li>
            <li>Right-click the folder and select "Share"</li>
            <li>Set sharing permissions as needed</li>
            <li>Copy the folder ID from the URL (the long string after /folders/)</li>
          </ol>
          <div className="bg-blue-50 p-3 rounded-lg">
            <p className="text-sm text-blue-800">
              <strong>Tip:</strong> The folder ID is the part after "/folders/" in the URL when you open the folder.
              <br />
              Example: <code className="text-xs">https://drive.google.com/drive/folders/1ABC...XYZ</code>
              <br />
              Folder ID: <code className="text-xs">1ABC...XYZ</code>
            </p>
          </div>
        </div>
      )
    },
    {
      title: "Environment Variables",
      content: (
        <div className="space-y-4">
          <p className="text-gray-700">
            Create a <code className="bg-gray-100 px-2 py-1 rounded">.env.local</code> file in your project root with the following variables:
          </p>
          <div className="bg-gray-900 text-green-400 p-4 rounded-lg font-mono text-sm">
            <div>REACT_APP_GOOGLE_API_KEY=your_api_key_here</div>
            <div>REACT_APP_GOOGLE_CLIENT_ID=your_client_id_here</div>
            <div>REACT_APP_GOOGLE_DRIVE_FOLDER_ID=your_folder_id_here</div>
          </div>
          <div className="bg-yellow-50 p-3 rounded-lg">
            <p className="text-sm text-yellow-800">
              <strong>Important:</strong> Never commit your .env.local file to version control. 
              Add it to your .gitignore file.
            </p>
          </div>
        </div>
      )
    },
    {
      title: "Deploy Configuration",
      content: (
        <div className="space-y-4">
          <p className="text-gray-700">
            When deploying your application, you'll need to configure these environment variables on your hosting platform:
          </p>
          
          <div className="space-y-4">
            <div className="border rounded-lg p-4">
              <h4 className="font-semibold text-gray-800 mb-2">Vercel</h4>
              <p className="text-sm text-gray-600">
                Go to your project settings → Environment Variables and add the three variables.
              </p>
            </div>
            
            <div className="border rounded-lg p-4">
              <h4 className="font-semibold text-gray-800 mb-2">Netlify</h4>
              <p className="text-sm text-gray-600">
                Go to Site settings → Environment variables and add the three variables.
              </p>
            </div>
            
            <div className="border rounded-lg p-4">
              <h4 className="font-semibold text-gray-800 mb-2">Other Platforms</h4>
              <p className="text-sm text-gray-600">
                Check your hosting platform's documentation for setting environment variables.
              </p>
            </div>
          </div>

          <div className="bg-green-50 p-3 rounded-lg">
            <p className="text-sm text-green-800">
              <strong>Security Note:</strong> The API key and client ID are safe to expose in client-side code 
              as they're designed for public use. However, make sure to configure proper domain restrictions 
              in your Google Cloud Console.
            </p>
          </div>
        </div>
      )
    }
  ];

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <div className="bg-white rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-2xl font-serif text-rose-800">
              Google Drive Setup Guide
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 p-2"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          {/* Progress indicator */}
          <div className="mt-4">
            <div className="flex items-center space-x-2">
              {steps.map((_, index) => (
                <div
                  key={index}
                  className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    index + 1 === currentStep
                      ? 'bg-rose-500 text-white'
                      : index + 1 < currentStep
                      ? 'bg-green-500 text-white'
                      : 'bg-gray-200 text-gray-600'
                  }`}
                >
                  {index + 1 < currentStep ? '✓' : index + 1}
                </div>
              ))}
            </div>
            <p className="text-sm text-gray-600 mt-2">
              Step {currentStep} of {steps.length}
            </p>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto" style={{ maxHeight: 'calc(90vh - 200px)' }}>
          <h3 className="text-xl font-semibold text-gray-800 mb-4">
            {steps[currentStep - 1].title}
          </h3>
          {steps[currentStep - 1].content}
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 flex justify-between">
          <button
            onClick={() => setCurrentStep(Math.max(1, currentStep - 1))}
            disabled={currentStep === 1}
            className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Previous
          </button>
          
          {currentStep < steps.length ? (
            <button
              onClick={() => setCurrentStep(Math.min(steps.length, currentStep + 1))}
              className="btn-primary"
            >
              Next
            </button>
          ) : (
            <button
              onClick={onClose}
              className="btn-primary"
            >
              Complete Setup
            </button>
          )}
        </div>
      </div>
    </div>
  );
};
